import { supabase } from '../utils/supabase';
import { CanvasNode, CanvasEdge, Tag } from '../types';
import { v4 as uuidv4 } from 'uuid';
import { canCreateProject } from './userProfileService';

// Project metadata interface
export interface Project {
  id: string;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
  is_public: boolean;
  tags?: Tag[]; // Add tags property to Project interface
}

// Project data interface
export interface ProjectData {
  nodes: CanvasNode[];
  edges: CanvasEdge[];
  tags?: Tag[];
  version: string;
  metadata: {
    projectId: string;
    projectName: string;
    lastSaved: string;
  };
}

// Create a new project
export async function createProject(name: string, description: string = ''): Promise<Project | null> {
  try {
    // Get the current user session
    const { data: { user } } = await supabase.auth.getUser();

    if (!user) {
      throw new Error('User not authenticated');
    }

    // Check if the user can create more projects based on their tier
    const canCreate = await canCreateProject();
    if (!canCreate) {
      throw new Error('You have reached the maximum number of projects allowed for your account tier. Please upgrade to create more projects.');
    }

    console.log('Creating project for user:', user.id);

    const projectId = uuidv4();

    // Create project metadata
    const { data, error } = await supabase
      .from('projects')
      .insert({
        id: projectId,
        name,
        description,
        user_id: user.id,  // Set the user_id explicitly
        is_public: false
      })
      .select('*')
      .single();

    console.log('Project insert response:', { data, error });

    if (error) {
      console.error('Database error creating project:', error);
      // Create a more detailed error message
      const errorMessage = `Database error: ${error.message || 'Unknown database error'}`;
      const enhancedError = new Error(errorMessage);
      // @ts-ignore - Add additional properties to the error
      enhancedError.originalError = error;
      throw enhancedError;
    }

    // Create empty project data in storage
    const emptyProject: ProjectData = {
      nodes: [],
      edges: [],
      tags: [],
      version: '1.0',
      metadata: {
        projectId,
        projectName: name,
        lastSaved: new Date().toISOString()
      }
    };

    // Save to storage
    const { error: storageError } = await supabase
      .storage
      .from('project-files')
      .upload(`${projectId}/project.json`, JSON.stringify(emptyProject));

    if (storageError) {
      console.error('Storage error creating project:', storageError);
      // Rollback project creation
      await supabase.from('projects').delete().eq('id', projectId);

      // Create a more detailed error message
      const errorMessage = `Storage error: ${storageError.message || 'Unknown storage error'}`;
      const enhancedError = new Error(errorMessage);
      // @ts-ignore - Add additional properties to the error
      enhancedError.originalError = storageError;
      throw enhancedError;
    }

    return data;
  } catch (error: any) {
    console.error('Error creating project:', error);

    // Create a standardized error message
    let errorMessage = 'Failed to create project';

    if (error) {
      if (typeof error === 'object') {
        if (error.message) {
          errorMessage = error.message;
        }

        // Log additional details if available
        if (error.code) console.error('Error code:', error.code);
        if (error.details) console.error('Error details:', error.details);
        if (error.hint) console.error('Error hint:', error.hint);
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
    }

    // Rethrow with a standardized format
    throw new Error(errorMessage);
  }
}

// Get all projects for the current user
export async function getUserProjects(): Promise<Project[]> {
  try {
    // Get the current user to log for debugging
    const { data: { user } } = await supabase.auth.getUser();
    console.log('Getting projects for user:', user?.id);

    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching projects:', error);
      throw new Error(`Failed to fetch projects: ${error.message}`);
    }

    console.log(`Found ${data?.length || 0} projects`);
    return data || [];
  } catch (error) {
    console.error('Error fetching user projects:', error);
    return [];
  }
}

// Get a project by ID
export async function getProject(projectId: string): Promise<Project | null> {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .eq('id', projectId)
      .single();

    if (error) {
      throw error;
    }

    return data;
  } catch (error) {
    console.error(`Error fetching project ${projectId}:`, error);
    return null;
  }
}

// Get project data (nodes and edges)
export async function getProjectData(projectId: string): Promise<ProjectData | null> {
  try {
    const { data, error } = await supabase
      .storage
      .from('project-files')
      .download(`${projectId}/project.json`);

    if (error) {
      throw error;
    }

    const text = await data.text();
    const projectData = JSON.parse(text) as ProjectData;

    // Task 1: Auto-delete projects with minimal nodes that require migration
    if (projectData && shouldAutoDeleteProject(projectData)) {
      console.log(`Auto-deleting project ${projectId} - minimal nodes requiring migration`);
      await deleteProject(projectId);
      return null;
    }

    return projectData;
  } catch (error) {
    console.error(`Error fetching project data ${projectId}:`, error);
    return null;
  }
}

// Helper function to determine if a project should be auto-deleted
function shouldAutoDeleteProject(projectData: ProjectData): boolean {
  const nodeCount = projectData.nodes?.length || 0;

  // Only delete projects with exactly 1 or 3 nodes
  if (nodeCount !== 1 && nodeCount !== 3) {
    return false;
  }

  // Check if project requires migration (has old-style node IDs)
  const requiresMigration = projectData.nodes?.some(node => {
    // Check if node ID matches hierarchical ID pattern (old style)
    // Old style nodes have IDs like "0", "0-1", "0-1-2", etc.
    return node.id === '0' ||
           /^0(-\d+)*$/.test(node.id) ||
           // Also check if the node has a label that suggests it's using hierarchical IDs as node IDs
           (node.label && (node.label === 'Root' || node.label.startsWith('Root-')) && node.id === node.label.replace('Root', '0').replace(/^0$/, '0'));
  }) || false;

  if (requiresMigration) {
    console.log(`Project requires migration and has ${nodeCount} nodes - marking for auto-deletion`);
    return true;
  }

  return false;
}

// Save project data
export async function saveProjectData(
  projectId: string,
  nodes: CanvasNode[],
  edges: CanvasEdge[],
  projectName: string,
  customProjectData?: ProjectData
): Promise<boolean> {
  try {
    // Update project metadata
    const { error: updateError } = await supabase
      .from('projects')
      .update({
        updated_at: new Date().toISOString(),
        name: projectName
      })
      .eq('id', projectId);

    if (updateError) {
      throw updateError;
    }

    // Use custom project data if provided, otherwise create a new one
    const projectData: ProjectData = customProjectData || {
      nodes,
      edges,
      tags: [], // Will be populated by TagContext
      version: '1.0',
      metadata: {
        projectId,
        projectName,
        lastSaved: new Date().toISOString()
      }
    };

    // Save to storage
    const { error: storageError } = await supabase
      .storage
      .from('project-files')
      .upload(`${projectId}/project.json`, JSON.stringify(projectData), {
        upsert: true
      });

    if (storageError) {
      throw storageError;
    }

    return true;
  } catch (error) {
    console.error(`Error saving project data ${projectId}:`, error);
    return false;
  }
}

// Delete a project
export async function deleteProject(projectId: string): Promise<boolean> {
  try {
    // Delete project metadata
    const { error } = await supabase
      .from('projects')
      .delete()
      .eq('id', projectId);

    if (error) {
      throw error;
    }

    // Delete project files
    const { error: storageError } = await supabase
      .storage
      .from('project-files')
      .remove([`${projectId}/project.json`]);

    if (storageError) {
      console.error('Error deleting project files:', storageError);
      // Continue anyway, as the metadata is already deleted
    }

    return true;
  } catch (error) {
    console.error(`Error deleting project ${projectId}:`, error);
    return false;
  }
}
