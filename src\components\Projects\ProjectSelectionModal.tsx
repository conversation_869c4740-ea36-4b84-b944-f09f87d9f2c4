import React, { useState, useRef, useEffect } from 'react';
import { Dialog } from 'primereact/dialog';
import { But<PERSON> } from 'primereact/button';
import { InputText } from 'primereact/inputtext';
import { Toast } from 'primereact/toast';
import { ConfirmDialog, confirmDialog } from 'primereact/confirmdialog';
import { useProject } from '../../context/ProjectContext';
import { useGraph } from '../../context/GraphContext';
import { useTestRun } from '../../context/TestRunContext';
import { useAuth } from '../../context/AuthContext';
import { useAuthWrapper } from '../../context/AuthWrapper';
import { TierLevel, getMaxProjectsAllowed } from '../../services/userProfileService';
import DryRunWarningModal from './DryRunWarningModal';
import { saveProjectData, deleteProject } from '../../services/projectService';
import { calculateGraphLayout } from '../../utils/graphLayout';
import { GraphLayout } from '../../types/layouts';
import { CanvasNode, EditorSegment } from '../../types';
import './ProjectSelectionModal.css';

interface ProjectSelectionModalProps {
  visible: boolean;
  onHide: () => void;
  onCreateNewProject: () => void; // This is now used for test run mode only
  onImport: () => void;
  onOpenProject: (projectId: string) => void;
  onLoadDemo?: () => void; // Added for loading demo template
}

const ProjectSelectionModal: React.FC<ProjectSelectionModalProps> = ({
  visible,
  onHide,
  onCreateNewProject,
  onImport,
  onOpenProject,
  onLoadDemo
}) => {
  const { projects, createNewProject, loadProject, saveCurrentProject, currentProject, setCurrentProject, error: projectError, loadProjects } = useProject();
  const { nodes, edges, setNodes, setEdges } = useGraph();
  const { isTestRun, setTestRun } = useTestRun();
  // Use AuthWrapper instead of direct Auth context
  const { isAuthenticated, user, isFree, isPaid, isAdmin } = useAuthWrapper();
  const [showDryRunWarningModal, setShowDryRunWarningModal] = useState(false);
  const [showNewProjectDialog, setShowNewProjectDialog] = useState(false);
  const [newProjectName, setNewProjectName] = useState('');
  const [newProjectDescription, setNewProjectDescription] = useState('');
  const [loading, setLoading] = useState(false);
  const [deletingProjectId, setDeletingProjectId] = useState<string | null>(null);
  const toast = useRef<Toast>(null);

  // Handle export for Dry Run warning modal
  const handleExport = () => {
    // Close the warning modal
    setShowDryRunWarningModal(false);

    // Trigger export by clicking the export button
    onImport(); // This will open the import/export modal

    // Set the mode to export
    setTimeout(() => {
      const exportTab = document.querySelector('.p-tabview-nav li:nth-child(2)');
      if (exportTab) {
        (exportTab as HTMLElement).click();
      }
    }, 100);
  };

  // Handle upgrade for Dry Run warning modal
  const handleUpgrade = () => {
    // Close the warning modal
    setShowDryRunWarningModal(false);

    // Show upgrade notification
    toast.current?.show({
      severity: 'info',
      summary: 'Upgrade',
      detail: 'Contact administrator to upgrade your account.',
      life: 5000
    });
  };

  // Function to handle proceeding with new project creation after warning
  const handleProceedWithNewProject = () => {
    // Close the warning modal
    setShowDryRunWarningModal(false);

    // In dry run mode, create a root node with a default title
    createRootNodeWithProjectName('My Project');
    // In dry run mode, we don't save to the database
    onHide();
  };

  const handleCreateNewProject = async () => {
    // Different behavior based on user tier and state

    // Case 1: User is in Dry Run mode and tries to create a new project
    if (isTestRun && nodes.length > 0) {
      // Show warning modal
      setShowDryRunWarningModal(true);
      return;
    }

    // Case 2: User is Tier 0 (Admin) or Tier 1 (Paid) - Show project creation dialog
    if (isAuthenticated && (isAdmin || isPaid)) {
      // Show the dialog to enter project name
      setShowNewProjectDialog(true);
      return;
    }

    // Case 3: User is Tier 2 (Free) with no projects - Show project creation dialog
    if (isAuthenticated && isFree && (!projects || projects.length === 0)) {
      // Show the dialog to enter project name
      setShowNewProjectDialog(true);
      return;
    }

    // Case 4: User is Tier 2 (Free) with one project - Enter true Dry Run mode (as if unsigned)
    if (isAuthenticated && isFree && projects && projects.length >= getMaxProjectsAllowed(TierLevel.Free)) {
      console.log('T2 user with max projects, entering true Dry Run mode from modal');

      // First clear the canvas
      setNodes([]);
      setEdges([]);

      // Unselect current project to prevent accidental overwriting
      setCurrentProject(null);

      // Enter Dry Run mode - this will trigger the dry-run-mode-changed event
      // which will apply the auth override
      // We need to ensure this is treated as an explicit Dry Run request
      console.log('T2 user explicitly entering Dry Run mode from modal');
      setTestRun(true);

      // Show toast notification
      toast.current?.show({
        severity: 'info',
        summary: 'Dry Run Mode',
        detail: 'Free accounts are limited to one project. You\'ve entered Dry Run mode where changes won\'t be saved to your account.',
        life: 8000
      });

      // After a short delay to ensure Dry Run mode is fully applied
      setTimeout(() => {
        // In dry run mode, create a root node with a default title
        createRootNodeWithProjectName('My Project');
      }, 100);

      // In dry run mode, we don't save to the database
      onHide();
      return;
    }

    // Default case: Just create a root node (for unsigned users or other cases)
    if (isTestRun) {
      // In test run mode, create a root node with a default title
      createRootNodeWithProjectName('My Project');
      // In test run mode, we don't save to the database
      onHide();
      return;
    }

    // For authenticated users, show the dialog to enter project name
    setShowNewProjectDialog(true);
  };

  const handleCreateProject = async () => {
    if (!newProjectName.trim()) {
      toast.current?.show({
        severity: 'warn',
        summary: 'Warning',
        detail: 'Project name is required'
      });
      return;
    }

    setLoading(true);
    const newProject = await createNewProject(newProjectName, newProjectDescription);
    setLoading(false);

    if (newProject) {
      setShowNewProjectDialog(false);
      setNewProjectName('');
      setNewProjectDescription('');
      toast.current?.show({
        severity: 'success',
        summary: 'Success',
        detail: 'Project created successfully'
      });

      // First make sure the project is set as current project
      console.log('Setting current project:', newProject);
      // Explicitly set the current project to ensure it's selected in the sidebar
      setCurrentProject(newProject);

      // Then load the empty project
      console.log('Loading project with ID:', newProject.id);
      const loadSuccess = await loadProject(newProject.id);
      console.log('Project loaded successfully:', loadSuccess);

      // Then create a root node with the project name
      console.log('Creating root node with project name:', newProjectName);
      createRootNodeWithProjectName(newProjectName);

      // Make sure the project is still selected before saving
      if (!currentProject || currentProject.id !== newProject.id) {
        console.log('Project not selected, re-selecting before save');
        setCurrentProject(newProject);
      }

      // Force save the project with the root node
      console.log('Saving project with root node...');
      const saveSuccess = await saveCurrentProject();
      console.log('Project saved successfully:', saveSuccess);

      // If save failed, try one more time immediately
      if (!saveSuccess) {
        console.log('First save attempt failed, trying again immediately...');
        const retrySuccess = await saveCurrentProject();
        console.log('Retry save result:', retrySuccess);

        // If still failing, try direct API call as last resort
        if (!retrySuccess && user) {
          console.log('Using direct API call as last resort');
          try {
            // Make sure the project is still selected
            if (!currentProject || currentProject.id !== newProject.id) {
              console.log('Project not selected before direct API call, re-selecting');
              setCurrentProject(newProject);
            }

            const directSaveSuccess = await saveProjectData(
              newProject.id,
              nodes,
              edges,
              newProjectName
            );
            console.log('Direct API save result:', directSaveSuccess);
          } catch (error) {
            console.error('Direct API save failed:', error);
          }
        }
      }

      // Wait a moment to ensure everything is properly initialized before allowing interaction
      await new Promise(resolve => setTimeout(resolve, 500));

      // Force another layout update to ensure the graph is properly rendered
      window.dispatchEvent(new CustomEvent('graph-layout-updated'));

      // Hide the modal after project is created and saved
      onHide();
    } else {
      // Show error toast with the error message from context
      toast.current?.show({
        severity: 'error',
        summary: 'Error',
        detail: projectError || 'Failed to create project'
      });

      console.error('Project creation failed with error:', projectError);
    }
  };

  const handleImport = () => {
    onImport();
    onHide();
  };

  const handleOpenProject = (projectId: string) => {
    onOpenProject(projectId);
    onHide();
  };

  const handleLoadDemo = () => {
    if (onLoadDemo) {
      onLoadDemo();
      onHide();
    }
  };

  // Task 2: Handle project deletion
  const handleDeleteProject = (projectId: string, projectName: string, event: React.MouseEvent) => {
    // Prevent the project from being opened when delete button is clicked
    event.stopPropagation();

    confirmDialog({
      message: `Are you sure you want to delete the project "${projectName}"? This action cannot be undone.`,
      header: 'Delete Project',
      icon: 'pi pi-exclamation-triangle',
      acceptClassName: 'p-button-danger',
      accept: async () => {
        try {
          setDeletingProjectId(projectId);

          // Delete the project
          const success = await deleteProject(projectId);

          if (success) {
            toast.current?.show({
              severity: 'success',
              summary: 'Success',
              detail: `Project "${projectName}" has been deleted.`,
              life: 3000
            });

            // Reload the projects list
            await loadProjects();

            // If the deleted project was the current project, clear it
            if (currentProject?.id === projectId) {
              setCurrentProject(null);
              setNodes([]);
              setEdges([]);
            }
          } else {
            throw new Error('Failed to delete project');
          }
        } catch (error) {
          console.error('Error deleting project:', error);
          toast.current?.show({
            severity: 'error',
            summary: 'Error',
            detail: `Failed to delete project "${projectName}". Please try again.`,
            life: 5000
          });
        } finally {
          setDeletingProjectId(null);
        }
      }
    });
  };

  // Create a root node with the project name as title
  const createRootNodeWithProjectName = (projectName: string) => {
    // Clear any existing nodes
    setNodes([]);
    setEdges([]);

    // Create a default richtext segment for the root node
    const defaultSegment: EditorSegment = {
      id: Date.now(),
      type: 'richtext',
      content: ''
    };

    // Create a root node with the project name
    const rootNode: CanvasNode = {
      id: `node-${Date.now()}`,
      x: 0,
      y: 0,
      width: 336,
      height: 356,
      label: 'Root',
      title: projectName, // Use project name as the title
      content: '',
      color: '#4299e1',
      useCustomColor: true,  // Root node uses custom color by default
      inheritedColor: undefined,
      // Add segments to ensure the node is fully functional
      segments: [defaultSegment],
      relationships: {
        parentId: undefined,
        childIds: []
      }
    };

    console.log('Creating root node with ID:', rootNode.id);

    // Apply layout and set the node - force TOP_DOWN layout for root node
    const layoutedNodes = calculateGraphLayout([rootNode], GraphLayout.TOP_DOWN);

    // Set nodes synchronously
    setNodes(layoutedNodes);

    // Force a layout update event to ensure the graph is properly rendered
    setTimeout(() => {
      window.dispatchEvent(new CustomEvent('graph-layout-updated'));
    }, 100);
  };

  const renderContent = () => {
    return (
      <div className="project-selection-content">
        {/* Warning message */}
        <div className="project-selection-warning">
          <i className="pi pi-exclamation-triangle" style={{ marginRight: '8px', color: '#f59e0b' }}></i>
          <span>Warning: Selecting an option below will replace your current work if you have any unsaved changes.</span>
        </div>

        {/* New Project Button - Always shown */}
        <Button
          label="New Project"
          icon="pi pi-plus"
          className="p-button-lg project-selection-button"
          onClick={handleCreateNewProject}
        />

        {/* Import Button - Always shown */}
        <Button
          label="Import"
          icon="pi pi-download"
          className="p-button-lg project-selection-button"
          onClick={handleImport}
        />

        {/* Load Demo Button - Always shown */}
        <Button
          label="Load Demo"
          icon="pi pi-book"
          className="p-button-lg project-selection-button p-button-secondary"
          onClick={handleLoadDemo}
        />

        {/* Project List - Only shown for authenticated users */}
        {!isTestRun && projects && projects.length > 0 && (
          <div className="project-list-section">
            <h3>Open...</h3>
            <div className="project-buttons">
              {projects.map(project => (
                <div key={project.id} className="project-button-container">
                  <Button
                    label={project.name}
                    className="p-button project-button"
                    onClick={() => handleOpenProject(project.id)}
                  />
                  <Button
                    icon="pi pi-trash"
                    className="p-button-danger p-button-sm project-delete-button"
                    onClick={(e) => handleDeleteProject(project.id, project.name, e)}
                    loading={deletingProjectId === project.id}
                    tooltip="Delete Project"
                    tooltipOptions={{ position: 'top' }}
                  />
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Close button */}
        <div className="project-selection-footer">
          <Button
            label="Close"
            icon="pi pi-times"
            className="p-button-outlined p-button-danger close-button"
            onClick={onHide}
          />
        </div>
      </div>
    );
  };

  // Remove the auto-close effect - let users see their project options
  // Tier 2 users should be able to see their one project in the modal

  return (
    <>
      <Toast ref={toast} />
      <ConfirmDialog />
      <Dialog
        visible={visible}
        onHide={onHide}
        header={isTestRun ? "Dry Run Mode" : "Welcome"}
        className="project-selection-modal"
        style={{ width: '500px' }}
        modal
        blockScroll={true}
        dismissableMask={false}
        closeOnEscape={false}
        closable={true}
      >
        {renderContent()}
      </Dialog>

      {/* Dry Run Warning Modal */}
      <DryRunWarningModal
        visible={showDryRunWarningModal}
        onHide={() => setShowDryRunWarningModal(false)}
        onProceed={handleProceedWithNewProject}
        onExport={handleExport}
        onUpgrade={handleUpgrade}
      />

      {/* New Project Dialog */}
      <Dialog
        header="Create New Project"
        visible={showNewProjectDialog}
        onHide={() => setShowNewProjectDialog(false)}
        style={{ width: '450px' }}
        modal
        footer={(
          <div>
            <Button
              label="Cancel"
              icon="pi pi-times"
              className="p-button-text"
              onClick={() => setShowNewProjectDialog(false)}
              disabled={loading}
            />
            <Button
              label="Create"
              icon="pi pi-check"
              onClick={handleCreateProject}
              loading={loading}
            />
          </div>
        )}
      >
        <div className="p-fluid">
          <div className="p-field">
            <label htmlFor="name">Project Name</label>
            <InputText
              id="name"
              value={newProjectName}
              onChange={(e) => setNewProjectName(e.target.value)}
              required
              autoFocus
            />
          </div>
          <div className="p-field" style={{ marginTop: '1rem' }}>
            <label htmlFor="description">Description (Optional)</label>
            <InputText
              id="description"
              value={newProjectDescription}
              onChange={(e) => setNewProjectDescription(e.target.value)}
            />
          </div>
        </div>
      </Dialog>
    </>
  );
};

export default ProjectSelectionModal;
