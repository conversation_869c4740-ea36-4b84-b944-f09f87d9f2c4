/**
 * Styles for node dragging behavior
 */

/* When a node is being dragged, disable canvas panning */
body.node-dragging {
  /* Prevent text selection during dragging */
  user-select: none;
}

/* CRITICAL FIX: Disable ALL transitions during node dragging for real-time cursor following */
body.node-dragging .node,
body.node-dragging .custom-node,
body.node-dragging .custom-node-container,
body.node-dragging .canvas-node,
body.node-dragging .simple-node {
  transition: none !important;
  transform-origin: 0 0 !important;
  will-change: transform !important;
}

/* Ensure nodes have higher z-index than canvas during dragging */
body.node-dragging .node,
body.node-dragging .simple-node {
  z-index: 1000;
}

/* Disable canvas panning when a node is being dragged */
body.node-dragging .canvas-container {
  pointer-events: none;
}

/* But allow nodes to still receive events */
body.node-dragging .node,
body.node-dragging .simple-node {
  pointer-events: auto;
}

/* Ensure node buttons still work during dragging */
body.node-dragging .node-button {
  pointer-events: auto;
}
