.project-selection-modal {
  background-color: var(--modal-bg) !important;
  color: var(--text-primary) !important;
  box-shadow: 0 4px 20px var(--shadow-color-strong) !important;
  border-radius: 8px !important;
  transition: background-color var(--transition-speed), color var(--transition-speed), box-shadow var(--transition-speed);
}

.project-selection-modal .p-dialog-header {
  border-bottom: 1px solid var(--border-color);
  padding: 1.5rem;
  background-color: var(--modal-bg) !important;
  color: var(--text-primary) !important;
  transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

.project-selection-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  padding: 1rem;
}

.project-selection-button {
  width: 100%;
  justify-content: center;
  padding: 1rem;
  font-size: 1.1rem;
  margin-bottom: 0.5rem;
  transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

.project-list-section {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.project-list-section h3 {
  margin: 0;
  padding: 0;
  font-size: 1.2rem;
  color: var(--text-primary);
  transition: color var(--transition-speed);
}

.project-buttons {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  max-height: 300px;
  overflow-y: auto;
}

.project-button-container {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
}

.project-button {
  flex: 1;
  text-align: left;
  justify-content: flex-start;
  padding: 0.75rem;
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  color: var(--text-primary);
  transition: background-color var(--transition-speed), color var(--transition-speed), border-color var(--transition-speed);
}

.project-button:hover {
  background-color: var(--bg-tertiary);
}

.project-delete-button {
  flex-shrink: 0;
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #dc2626;
  border-color: #dc2626;
  color: white;
  transition: background-color var(--transition-speed), border-color var(--transition-speed);
}

.project-delete-button:hover {
  background-color: #b91c1c;
  border-color: #b91c1c;
}

.project-delete-button:focus {
  box-shadow: 0 0 0 2px rgba(220, 38, 38, 0.2);
}

.project-selection-warning {
  display: flex;
  align-items: center;
  padding: 0.75rem;
  background-color: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  border-radius: 6px;
  margin-bottom: 0.5rem;
  font-size: 0.9rem;
  line-height: 1.4;
}

.project-selection-footer {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--border-color);
}

.close-button {
  min-width: 120px;
}
